# 桌面窗口跟随翻译程序

一个基于AI的智能桌面翻译工具，支持剪贴板自动监控和鼠标跟随显示。

## 功能特点

- 🤖 **AI智能翻译**: 支持OpenAI GPT和Anthropic Claude多种AI翻译服务
- 📋 **剪贴板监控**: 自动检测剪贴板文本变化，无需手动操作
- 🖱️ **鼠标跟随**: 翻译结果悬浮窗智能跟随鼠标位置显示
- 🚀 **高性能**: 内置缓存机制，避免重复翻译
- 🎨 **美观界面**: 透明悬浮窗设计，不影响正常使用
- 🌐 **多语言**: 支持中英日韩等多种语言互译
- ⚙️ **灵活配置**: 丰富的配置选项，满足个性化需求

## 安装说明

### 环境要求
- Node.js 18.0+
- Windows 10/11 (主要支持)
- macOS 10.15+ (部分支持)
- Linux Ubuntu 20.04+ (实验性支持)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 桌面窗口翻译程序
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的API密钥：
```env
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

4. **启动程序**
```bash
npm start
```

## 使用方法

1. **启动程序**: 运行后会在系统托盘显示图标
2. **复制文本**: 使用 Ctrl+C 复制任何需要翻译的文本
3. **查看翻译**: 翻译结果会自动在鼠标附近的悬浮窗中显示
4. **系统托盘**: 右键托盘图标可以启停程序或退出

## 配置选项

程序配置文件位于 `config/settings.json`：

```json
{
  "translation": {
    "defaultTargetLanguage": "zh",  // 默认目标语言
    "cacheExpiryMinutes": 60,       // 缓存过期时间(分钟)
    "maxTextLength": 2000,          // 最大文本长度
    "debounceDelay": 500           // 防抖延迟(毫秒)
  },
  "ui": {
    "windowOpacity": 0.9,           // 窗口透明度
    "windowTimeoutMs": 5000,        // 窗口显示时长
    "fontSize": 14,                 // 字体大小
    "fontFamily": "Microsoft YaHei" // 字体族
  },
  "ai": {
    "primaryService": "openai",     // 主要翻译服务
    "fallbackService": "anthropic", // 备用翻译服务
    "model": "gpt-3.5-turbo",      // AI模型
    "maxRetries": 3                // 最大重试次数
  }
}
```

## 支持的翻译服务

### OpenAI
- 模型: gpt-3.5-turbo, gpt-4
- 需要: OPENAI_API_KEY

### Anthropic Claude
- 模型: claude-3-haiku-20240307
- 需要: ANTHROPIC_API_KEY

## 开发说明

### 项目结构
```
src/
├── modules/           # 核心模块
│   ├── ClipboardMonitor.js    # 剪贴板监控
│   ├── TextProcessor.js      # 文本处理
│   ├── MouseTracker.js       # 鼠标跟踪
│   └── FloatingWindow.js     # 悬浮窗显示
├── services/          # 服务层
│   └── AITranslationService.js # AI翻译服务
├── utils/             # 工具函数
│   └── helpers.js
└── main.js           # 主程序入口
```

### 开发命令
```bash
npm run dev     # 开发模式运行
npm test        # 运行测试
npm run build   # 构建可执行文件
```

### 构建发布
```bash
npm run build
```
构建后的文件位于 `build/` 目录。

## 常见问题

### Q: 程序无法启动
A: 检查Node.js版本，确保>=18.0，并正确安装了依赖

### Q: 翻译不工作
A: 检查API密钥配置是否正确，网络连接是否正常

### Q: 悬浮窗不显示
A: 检查系统权限，某些安全软件可能阻止悬浮窗显示

### Q: 支持哪些语言
A: 支持AI模型能识别的所有语言，主要包括中英日韩等

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础翻译功能
- 剪贴板监控和鼠标跟随
- AI翻译服务集成