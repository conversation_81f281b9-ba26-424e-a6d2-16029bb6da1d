require('dotenv').config();
const { app, Menu, Tray, nativeImage } = require('electron');
const path = require('path');
const fs = require('fs');

const ClipboardMonitor = require('./modules/ClipboardMonitor');
const TextProcessor = require('./modules/TextProcessor');
const AITranslationService = require('./services/AITranslationService');
const MouseTracker = require('./modules/MouseTracker');
const FloatingWindow = require('./modules/FloatingWindow');

class DesktopTranslator {
  constructor() {
    this.config = this.loadConfig();
    this.isRunning = false;
    
    this.clipboardMonitor = null;
    this.textProcessor = null;
    this.translationService = null;
    this.mouseTracker = null;
    this.floatingWindow = null;
    this.tray = null;
    
    this.translationQueue = [];
    this.isTranslating = false;
    
    this.initializeComponents();
    this.setupEventHandlers();
  }

  loadConfig() {
    try {
      const configPath = path.join(__dirname, '../config/settings.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('加载配置文件失败:', error);
      return this.getDefaultConfig();
    }
  }

  getDefaultConfig() {
    return {
      translation: {
        defaultTargetLanguage: "zh",
        cacheExpiryMinutes: 60,
        maxTextLength: 2000,
        debounceDelay: 500
      },
      ui: {
        windowOpacity: 0.9,
        windowTimeoutMs: 5000,
        fontSize: 14,
        fontFamily: "Microsoft YaHei",
        backgroundColor: "#000000",
        textColor: "#ffffff",
        borderRadius: 5,
        padding: 10
      },
      ai: {
        primaryService: "openai",
        fallbackService: "anthropic",
        model: "gpt-3.5-turbo",
        maxRetries: 3,
        timeout: 10000
      }
    };
  }

  initializeComponents() {
    this.clipboardMonitor = new ClipboardMonitor({
      checkInterval: 500,
      minTextLength: 2,
      maxTextLength: this.config.translation.maxTextLength
    });

    this.textProcessor = new TextProcessor({
      maxLength: this.config.translation.maxTextLength,
      minLength: 2,
      cacheExpiry: this.config.translation.cacheExpiryMinutes * 60 * 1000
    });

    this.translationService = new AITranslationService({
      config: this.config.ai,
      cacheExpiry: this.config.translation.cacheExpiryMinutes * 60 * 1000
    });

    this.mouseTracker = new MouseTracker({
      trackingInterval: 100
    });

    this.floatingWindow = new FloatingWindow({
      width: 400,
      height: 150,
      opacity: this.config.ui.windowOpacity,
      timeout: this.config.ui.windowTimeoutMs,
      fontSize: this.config.ui.fontSize,
      fontFamily: this.config.ui.fontFamily,
      backgroundColor: this.config.ui.backgroundColor,
      textColor: this.config.ui.textColor,
      borderRadius: this.config.ui.borderRadius,
      padding: this.config.ui.padding
    });
  }

  setupEventHandlers() {
    this.clipboardMonitor.on('textCopied', (data) => {
      this.handleTextCopied(data);
    });

    this.clipboardMonitor.on('error', (error) => {
      console.error('剪贴板监控错误:', error);
    });

    this.mouseTracker.on('positionChanged', (position) => {
      this.handleMousePositionChanged(position);
    });

    this.mouseTracker.on('error', (error) => {
      console.error('鼠标跟踪错误:', error);
    });
  }

  async handleTextCopied(data) {
    try {
      console.log('检测到文本复制:', data.text.substring(0, 50) + '...');
      
      const processedText = this.textProcessor.process(data.text);
      if (!processedText) {
        console.log('文本处理失败或无效');
        return;
      }

      if (this.textProcessor.isDuplicate(processedText.original)) {
        console.log('重复文本，跳过翻译');
        return;
      }

      const translationData = this.textProcessor.formatForTranslation(processedText);
      
      this.addToTranslationQueue(translationData);
      
    } catch (error) {
      console.error('处理复制文本时出错:', error);
    }
  }

  addToTranslationQueue(translationData) {
    this.translationQueue.push(translationData);
    
    if (!this.isTranslating) {
      this.processTranslationQueue();
    }
  }

  async processTranslationQueue() {
    if (this.translationQueue.length === 0) {
      this.isTranslating = false;
      return;
    }

    this.isTranslating = true;
    const translationData = this.translationQueue.shift();

    try {
      const mousePosition = this.mouseTracker.getCurrentPosition();
      const windowPosition = this.mouseTracker.calculateOptimalWindowPosition(mousePosition);
      
      this.floatingWindow.showLoading('正在翻译...');
      this.floatingWindow.show('正在翻译...', windowPosition);

      const result = await this.translationService.translate(
        translationData.text,
        translationData.sourceLanguage,
        translationData.targetLanguage
      );

      this.floatingWindow.updateContent(result.translatedText);
      
      console.log(`翻译完成: ${translationData.text.substring(0, 30)}... -> ${result.translatedText.substring(0, 30)}...`);
      
    } catch (error) {
      console.error('翻译失败:', error);
      this.floatingWindow.updateContent(`翻译失败: ${error.message}`);
    }

    setTimeout(() => {
      this.processTranslationQueue();
    }, this.config.translation.debounceDelay);
  }

  handleMousePositionChanged(position) {
    if (this.floatingWindow && this.floatingWindow.isWindowVisible()) {
      const windowPosition = this.mouseTracker.calculateOptimalWindowPosition(position);
      this.floatingWindow.updatePosition(windowPosition);
    }
  }

  start() {
    if (this.isRunning) {
      return;
    }

    try {
      this.clipboardMonitor.start();
      this.mouseTracker.start();
      
      this.isRunning = true;
      console.log('桌面翻译程序已启动');
      
    } catch (error) {
      console.error('启动程序时出错:', error);
    }
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    try {
      this.clipboardMonitor.stop();
      this.mouseTracker.stop();
      this.floatingWindow.hide();
      
      this.isRunning = false;
      console.log('桌面翻译程序已停止');
      
    } catch (error) {
      console.error('停止程序时出错:', error);
    }
  }

  createTray() {
    const iconPath = path.join(__dirname, '../assets/icon.ico');
    let trayIcon;
    
    try {
      trayIcon = nativeImage.createFromPath(iconPath);
    } catch (error) {
      trayIcon = nativeImage.createEmpty();
    }

    this.tray = new Tray(trayIcon);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '桌面翻译程序',
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: this.isRunning ? '停止翻译' : '开始翻译',
        click: () => {
          if (this.isRunning) {
            this.stop();
          } else {
            this.start();
          }
          this.updateTrayMenu();
        }
      },
      {
        label: '清除缓存',
        click: () => {
          this.translationService.clearCache();
          console.log('翻译缓存已清除');
        }
      },
      {
        type: 'separator'
      },
      {
        label: '退出',
        click: () => {
          this.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('桌面翻译程序');
  }

  updateTrayMenu() {
    if (!this.tray) return;
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '桌面翻译程序',
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: this.isRunning ? '停止翻译' : '开始翻译',
        click: () => {
          if (this.isRunning) {
            this.stop();
          } else {
            this.start();
          }
          this.updateTrayMenu();
        }
      },
      {
        label: '清除缓存',
        click: () => {
          this.translationService.clearCache();
          console.log('翻译缓存已清除');
        }
      },
      {
        type: 'separator'
      },
      {
        label: '退出',
        click: () => {
          this.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
  }

  quit() {
    this.stop();
    
    if (this.floatingWindow) {
      this.floatingWindow.destroy();
    }
    
    if (this.tray) {
      this.tray.destroy();
    }
    
    app.quit();
  }
}

const translator = new DesktopTranslator();

app.whenReady().then(() => {
  translator.createTray();
  translator.start();
});

app.on('window-all-closed', (e) => {
  e.preventDefault();
});

app.on('activate', () => {
  translator.start();
});

app.on('before-quit', () => {
  translator.quit();
});

process.on('SIGINT', () => {
  translator.quit();
});

process.on('SIGTERM', () => {
  translator.quit();
});