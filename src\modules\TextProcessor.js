class TextProcessor {
  constructor(options = {}) {
    this.maxLength = options.maxLength || 2000;
    this.minLength = options.minLength || 2;
    this.cache = new Map();
    this.cacheExpiry = options.cacheExpiry || 60 * 60 * 1000; // 1小时
  }

  process(text) {
    if (!this.isValidText(text)) {
      return null;
    }

    const processedText = {
      original: text,
      cleaned: this.cleanText(text),
      language: this.detectLanguage(text),
      hash: this.generateHash(text),
      timestamp: Date.now()
    };

    return processedText;
  }

  isValidText(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }

    const trimmed = text.trim();
    
    if (trimmed.length < this.minLength || trimmed.length > this.maxLength) {
      return false;
    }

    if (this.isNumberOnly(trimmed)) {
      return false;
    }

    if (this.isSpecialCharactersOnly(trimmed)) {
      return false;
    }

    return true;
  }

  cleanText(text) {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\s{2,}/g, ' ')
      .trim();
  }

  detectLanguage(text) {
    const chineseRegex = /[\u4e00-\u9fff]/g;
    const englishRegex = /[a-zA-Z]/g;
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/g;
    const koreanRegex = /[\uac00-\ud7af]/g;

    const chineseMatches = (text.match(chineseRegex) || []).length;
    const englishMatches = (text.match(englishRegex) || []).length;
    const japaneseMatches = (text.match(japaneseRegex) || []).length;
    const koreanMatches = (text.match(koreanRegex) || []).length;

    const totalLength = text.length;
    
    if (chineseMatches / totalLength > 0.3) {
      return 'zh';
    } else if (englishMatches / totalLength > 0.5) {
      return 'en';
    } else if (japaneseMatches / totalLength > 0.3) {
      return 'ja';
    } else if (koreanMatches / totalLength > 0.3) {
      return 'ko';
    }

    return 'auto';
  }

  isNumberOnly(text) {
    return /^\d+(\.\d+)?$/.test(text.trim());
  }

  isSpecialCharactersOnly(text) {
    return /^[^\w\u4e00-\u9fff]+$/.test(text.trim());
  }

  generateHash(text) {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  isDuplicate(text, timeWindow = 5000) {
    const hash = this.generateHash(text);
    const now = Date.now();
    
    if (this.cache.has(hash)) {
      const cachedTime = this.cache.get(hash);
      if (now - cachedTime < timeWindow) {
        return true;
      }
    }
    
    this.cache.set(hash, now);
    this.cleanExpiredCache();
    return false;
  }

  cleanExpiredCache() {
    const now = Date.now();
    for (const [hash, timestamp] of this.cache.entries()) {
      if (now - timestamp > this.cacheExpiry) {
        this.cache.delete(hash);
      }
    }
  }

  getTargetLanguage(sourceLanguage, defaultTarget = 'zh') {
    const languageMap = {
      'zh': 'en',
      'en': 'zh',
      'ja': 'zh',
      'ko': 'zh',
      'auto': defaultTarget
    };

    return languageMap[sourceLanguage] || defaultTarget;
  }

  formatForTranslation(processedText) {
    return {
      text: processedText.cleaned,
      sourceLanguage: processedText.language,
      targetLanguage: this.getTargetLanguage(processedText.language),
      hash: processedText.hash,
      metadata: {
        originalLength: processedText.original.length,
        cleanedLength: processedText.cleaned.length,
        timestamp: processedText.timestamp
      }
    };
  }
}

module.exports = TextProcessor;