const clipboardy = require('clipboardy');
const EventEmitter = require('events');

class ClipboardMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.isMonitoring = false;
    this.lastClipboardContent = '';
    this.checkInterval = options.checkInterval || 500;
    this.minTextLength = options.minTextLength || 2;
    this.maxTextLength = options.maxTextLength || 2000;
    this.intervalId = null;
  }

  start() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.lastClipboardContent = this.getClipboardContent();
    
    this.intervalId = setInterval(() => {
      this.checkClipboardChange();
    }, this.checkInterval);

    console.log('剪贴板监控已启动');
  }

  stop() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('剪贴板监控已停止');
  }

  async checkClipboardChange() {
    try {
      const currentContent = await this.getClipboardContent();
      
      if (currentContent !== this.lastClipboardContent) {
        this.lastClipboardContent = currentContent;
        
        if (this.isValidText(currentContent)) {
          this.emit('textCopied', {
            text: currentContent,
            timestamp: Date.now()
          });
        }
      }
    } catch (error) {
      this.emit('error', {
        message: '读取剪贴板失败',
        error: error
      });
    }
  }

  async getClipboardContent() {
    try {
      return await clipboardy.read();
    } catch (error) {
      console.error('获取剪贴板内容失败:', error);
      return '';
    }
  }

  isValidText(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }

    const trimmedText = text.trim();
    
    if (trimmedText.length < this.minTextLength) {
      return false;
    }

    if (trimmedText.length > this.maxTextLength) {
      return false;
    }

    const imagePattern = /^data:image\//i;
    if (imagePattern.test(trimmedText)) {
      return false;
    }

    const urlPattern = /^https?:\/\//i;
    if (urlPattern.test(trimmedText) && trimmedText.split(' ').length === 1) {
      return false;
    }

    return true;
  }

  isRunning() {
    return this.isMonitoring;
  }
}

module.exports = ClipboardMonitor;