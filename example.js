const DesktopTranslator = require('./src/main');

// 使用示例
console.log('桌面翻译程序使用示例');
console.log('========================');
console.log('1. 确保已配置API密钥在.env文件中');
console.log('2. 运行 npm start 启动程序');
console.log('3. 复制任何文本（Ctrl+C）');
console.log('4. 翻译结果会在鼠标附近显示');
console.log('');
console.log('支持的操作:');
console.log('- 右键系统托盘图标: 启停程序');
console.log('- 复制文本: 自动触发翻译');
console.log('- 鼠标移动: 翻译窗口自动跟随');
console.log('');
console.log('配置文件: config/settings.json');
console.log('环境变量: .env');
console.log('');