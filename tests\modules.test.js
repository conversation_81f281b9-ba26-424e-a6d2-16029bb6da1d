const ClipboardMonitor = require('../src/modules/ClipboardMonitor');
const TextProcessor = require('../src/modules/TextProcessor');

describe('ClipboardMonitor', () => {
  let monitor;

  beforeEach(() => {
    monitor = new ClipboardMonitor({
      checkInterval: 100,
      minTextLength: 2,
      maxTextLength: 100
    });
  });

  afterEach(() => {
    if (monitor && monitor.isRunning()) {
      monitor.stop();
    }
  });

  test('should create monitor instance', () => {
    expect(monitor).toBeDefined();
    expect(monitor.isRunning()).toBe(false);
  });

  test('should validate text correctly', () => {
    expect(monitor.isValidText('Hello')).toBe(true);
    expect(monitor.isValidText('H')).toBe(false); // 太短
    expect(monitor.isValidText('')).toBe(false); // 空字符串
    expect(monitor.isValidText('A'.repeat(101))).toBe(false); // 太长
  });

  test('should start and stop monitoring', () => {
    monitor.start();
    expect(monitor.isRunning()).toBe(true);
    
    monitor.stop();
    expect(monitor.isRunning()).toBe(false);
  });
});

describe('TextProcessor', () => {
  let processor;

  beforeEach(() => {
    processor = new TextProcessor({
      maxLength: 100,
      minLength: 2
    });
  });

  test('should create processor instance', () => {
    expect(processor).toBeDefined();
  });

  test('should validate text correctly', () => {
    expect(processor.isValidText('Hello world')).toBe(true);
    expect(processor.isValidText('H')).toBe(false);
    expect(processor.isValidText('123')).toBe(false); // 纯数字
    expect(processor.isValidText('!!!')).toBe(false); // 特殊字符
  });

  test('should clean text properly', () => {
    const dirtyText = '  Hello\r\n\r\nWorld  \n\n\n  ';
    const cleaned = processor.cleanText(dirtyText);
    expect(cleaned).toBe('Hello\n\nWorld');
  });

  test('should detect language', () => {
    expect(processor.detectLanguage('Hello world')).toBe('en');
    expect(processor.detectLanguage('你好世界')).toBe('zh');
    expect(processor.detectLanguage('こんにちは')).toBe('ja');
    expect(processor.detectLanguage('안녕하세요')).toBe('ko');
  });

  test('should process text correctly', () => {
    const result = processor.process('Hello world');
    expect(result).toHaveProperty('original');
    expect(result).toHaveProperty('cleaned');
    expect(result).toHaveProperty('language');
    expect(result).toHaveProperty('hash');
    expect(result.original).toBe('Hello world');
    expect(result.language).toBe('en');
  });

  test('should detect duplicates', () => {
    const text = 'Hello world';
    expect(processor.isDuplicate(text)).toBe(false);
    expect(processor.isDuplicate(text)).toBe(true); // 第二次应该是重复的
  });

  test('should format for translation', () => {
    const processedText = processor.process('Hello world');
    const formatted = processor.formatForTranslation(processedText);
    
    expect(formatted).toHaveProperty('text');
    expect(formatted).toHaveProperty('sourceLanguage');
    expect(formatted).toHaveProperty('targetLanguage');
    expect(formatted.sourceLanguage).toBe('en');
    expect(formatted.targetLanguage).toBe('zh');
  });
});