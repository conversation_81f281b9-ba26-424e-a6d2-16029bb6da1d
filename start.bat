@echo off
echo 正在启动桌面翻译程序...

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Node.js，请先安装Node.js 18.0或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查是否存在node_modules
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 检查环境配置文件
if not exist ".env" (
    echo 警告: 未找到.env配置文件
    echo 请复制.env.example为.env并配置API密钥
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo 已创建默认.env文件，请编辑并填入API密钥
    )
    echo.
    echo 按任意键继续（程序可能无法正常翻译）...
    pause >nul
)

REM 启动程序
echo 启动中...
npm start

if errorlevel 1 (
    echo.
    echo 程序异常退出，按任意键关闭...
    pause >nul
)