@echo off
chcp 65001 >nul
echo Starting Desktop Translator...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not found. Please install Node.js 18.0 or higher
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check environment configuration file
if not exist ".env" (
    echo Warning: .env configuration file not found
    echo Please copy .env.example to .env and configure API keys
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo Default .env file created. Please edit and add API keys
    )
    echo.
    echo Press any key to continue (translation may not work properly)...
    pause >nul
)

REM Start the program
echo Starting...
npm start

if errorlevel 1 (
    echo.
    echo Program exited with error. Press any key to close...
    pause >nul
)