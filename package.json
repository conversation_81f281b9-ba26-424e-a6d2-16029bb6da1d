{"name": "desktop-translator", "version": "1.0.0", "description": "AI-driven desktop translation tool with mouse following functionality", "main": "src/main.js", "scripts": {"start": "node src/main.js", "dev": "nodemon src/main.js", "test": "jest", "build": "pkg . --out-path=build"}, "keywords": ["translator", "AI", "desktop", "clipboard"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "electron": "^28.0.0", "clipboardy": "^4.0.0", "node-window-manager": "^2.2.4", "dotenv": "^16.0.0", "openai": "^4.0.0", "anthropic": "^0.14.0"}, "devDependencies": {"nodemon": "^3.0.0", "jest": "^29.0.0", "pkg": "^5.8.0"}, "pkg": {"targets": ["node18-win-x64"], "outputPath": "build"}}