const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const axios = require('axios');

class AITranslationService {
  constructor(options = {}) {
    this.config = options.config || {};
    this.cache = new Map();
    this.cacheExpiry = options.cacheExpiry || 60 * 60 * 1000;
    
    this.openai = null;
    this.anthropic = null;
    
    this.initializeServices();
  }

  initializeServices() {
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }

    if (process.env.ANTHROPIC_API_KEY) {
      this.anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
      });
    }
  }

  async translate(text, sourceLanguage = 'auto', targetLanguage = 'zh') {
    const cacheKey = this.generateCacheKey(text, sourceLanguage, targetLanguage);
    
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    const primaryService = this.config.primaryService || 'openai';
    const fallbackService = this.config.fallbackService || 'anthropic';

    let result = null;

    try {
      result = await this.translateWithService(text, sourceLanguage, targetLanguage, primaryService);
    } catch (error) {
      console.log(`主要服务${primaryService}失败，尝试备用服务${fallbackService}`);
      try {
        result = await this.translateWithService(text, sourceLanguage, targetLanguage, fallbackService);
      } catch (fallbackError) {
        throw new Error(`所有翻译服务都失败了: ${error.message}, ${fallbackError.message}`);
      }
    }

    if (result) {
      this.setCache(cacheKey, result);
    }

    return result;
  }

  async translateWithService(text, sourceLanguage, targetLanguage, service) {
    const maxRetries = this.config.maxRetries || 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        switch (service) {
          case 'openai':
            return await this.translateWithOpenAI(text, sourceLanguage, targetLanguage);
          case 'anthropic':
            return await this.translateWithAnthropic(text, sourceLanguage, targetLanguage);
          default:
            throw new Error(`不支持的翻译服务: ${service}`);
        }
      } catch (error) {
        lastError = error;
        console.log(`翻译尝试 ${attempt}/${maxRetries} 失败:`, error.message);
        
        if (attempt < maxRetries) {
          await this.delay(1000 * attempt);
        }
      }
    }

    throw lastError;
  }

  async translateWithOpenAI(text, sourceLanguage, targetLanguage) {
    if (!this.openai) {
      throw new Error('OpenAI API密钥未配置');
    }

    const prompt = this.buildTranslationPrompt(text, sourceLanguage, targetLanguage);

    const response = await this.openai.chat.completions.create({
      model: this.config.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的翻译助手。请提供准确、自然、符合目标语言习惯的翻译。只返回翻译结果，不要添加任何解释。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000,
    });

    const translation = response.choices[0]?.message?.content?.trim();
    
    if (!translation) {
      throw new Error('OpenAI返回空翻译结果');
    }

    return {
      translatedText: translation,
      service: 'openai',
      sourceLanguage,
      targetLanguage,
      timestamp: Date.now()
    };
  }

  async translateWithAnthropic(text, sourceLanguage, targetLanguage) {
    if (!this.anthropic) {
      throw new Error('Anthropic API密钥未配置');
    }

    const prompt = this.buildTranslationPrompt(text, sourceLanguage, targetLanguage);

    const response = await this.anthropic.messages.create({
      model: 'claude-3-haiku-20240307',
      max_tokens: 2000,
      temperature: 0.3,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    });

    const translation = response.content[0]?.text?.trim();
    
    if (!translation) {
      throw new Error('Anthropic返回空翻译结果');
    }

    return {
      translatedText: translation,
      service: 'anthropic',
      sourceLanguage,
      targetLanguage,
      timestamp: Date.now()
    };
  }

  buildTranslationPrompt(text, sourceLanguage, targetLanguage) {
    const languageNames = {
      'zh': '中文',
      'en': '英文',
      'ja': '日文',
      'ko': '韩文',
      'auto': '自动检测'
    };

    const sourceLang = languageNames[sourceLanguage] || sourceLanguage;
    const targetLang = languageNames[targetLanguage] || targetLanguage;

    return `请将以下${sourceLang}文本翻译为${targetLang}：

${text}

要求：
1. 翻译要准确、自然、流畅
2. 保持原文的语气和风格
3. 专业术语要准确
4. 只返回翻译结果，不要添加解释`;
  }

  generateCacheKey(text, sourceLanguage, targetLanguage) {
    const content = `${text}|${sourceLanguage}|${targetLanguage}`;
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  setCache(key, value) {
    this.cache.set(key, value);
    this.cleanExpiredCache();
  }

  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheExpiry) {
        this.cache.delete(key);
      }
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  clearCache() {
    this.cache.clear();
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = AITranslationService;