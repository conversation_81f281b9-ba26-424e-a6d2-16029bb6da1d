const EventEmitter = require('events');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

class MouseTracker extends EventEmitter {
  constructor(options = {}) {
    super();
    this.isTracking = false;
    this.currentPosition = { x: 0, y: 0 };
    this.trackingInterval = options.trackingInterval || 100;
    this.intervalId = null;
    this.screenBounds = { width: 0, height: 0 };
    
    this.initializeScreenInfo();
  }

  async initializeScreenInfo() {
    try {
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('wmic desktopmonitor get screenwidth,screenheight /value');
        const lines = stdout.split('\n');
        
        for (const line of lines) {
          if (line.includes('ScreenWidth=')) {
            this.screenBounds.width = parseInt(line.split('=')[1]);
          }
          if (line.includes('ScreenHeight=')) {
            this.screenBounds.height = parseInt(line.split('=')[1]);
          }
        }
      } else {
        this.screenBounds = { width: 1920, height: 1080 };
      }
    } catch (error) {
      console.error('获取屏幕信息失败:', error);
      this.screenBounds = { width: 1920, height: 1080 };
    }
  }

  start() {
    if (this.isTracking) {
      return;
    }

    this.isTracking = true;
    this.intervalId = setInterval(() => {
      this.updateMousePosition();
    }, this.trackingInterval);

    console.log('鼠标跟踪已启动');
  }

  stop() {
    if (!this.isTracking) {
      return;
    }

    this.isTracking = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('鼠标跟踪已停止');
  }

  async updateMousePosition() {
    try {
      const position = await this.getMousePosition();
      
      if (position.x !== this.currentPosition.x || position.y !== this.currentPosition.y) {
        this.currentPosition = position;
        this.emit('positionChanged', position);
      }
    } catch (error) {
      this.emit('error', {
        message: '获取鼠标位置失败',
        error: error
      });
    }
  }

  async getMousePosition() {
    if (process.platform === 'win32') {
      return this.getWindowsMousePosition();
    } else if (process.platform === 'darwin') {
      return this.getMacMousePosition();
    } else {
      return this.getLinuxMousePosition();
    }
  }

  async getWindowsMousePosition() {
    try {
      const powershellScript = `
        Add-Type -AssemblyName System.Windows.Forms
        $pos = [System.Windows.Forms.Cursor]::Position
        Write-Output "$($pos.X),$($pos.Y)"
      `;
      
      const { stdout } = await execAsync(`powershell -Command "${powershellScript}"`);
      const [x, y] = stdout.trim().split(',').map(Number);
      
      return { x, y };
    } catch (error) {
      throw new Error(`Windows鼠标位置获取失败: ${error.message}`);
    }
  }

  async getMacMousePosition() {
    try {
      const script = `
        osascript -e 'tell application "System Events" to return (get the mouse location)'
      `;
      
      const { stdout } = await execAsync(script);
      const matches = stdout.match(/(\d+), (\d+)/);
      
      if (matches) {
        return {
          x: parseInt(matches[1]),
          y: parseInt(matches[2])
        };
      }
      
      throw new Error('无法解析鼠标位置');
    } catch (error) {
      throw new Error(`Mac鼠标位置获取失败: ${error.message}`);
    }
  }

  async getLinuxMousePosition() {
    try {
      const { stdout } = await execAsync('xdotool getmouselocation --shell');
      const lines = stdout.split('\n');
      
      let x = 0, y = 0;
      for (const line of lines) {
        if (line.startsWith('X=')) {
          x = parseInt(line.split('=')[1]);
        }
        if (line.startsWith('Y=')) {
          y = parseInt(line.split('=')[1]);
        }
      }
      
      return { x, y };
    } catch (error) {
      throw new Error(`Linux鼠标位置获取失败: ${error.message}`);
    }
  }

  calculateOptimalWindowPosition(mousePos, windowSize = { width: 300, height: 100 }) {
    const margin = 10;
    let x = mousePos.x + margin;
    let y = mousePos.y + margin;

    if (x + windowSize.width > this.screenBounds.width) {
      x = mousePos.x - windowSize.width - margin;
    }

    if (y + windowSize.height > this.screenBounds.height) {
      y = mousePos.y - windowSize.height - margin;
    }

    if (x < 0) x = margin;
    if (y < 0) y = margin;

    return { x, y };
  }

  getCurrentPosition() {
    return { ...this.currentPosition };
  }

  getScreenBounds() {
    return { ...this.screenBounds };
  }

  isRunning() {
    return this.isTracking;
  }

  setTrackingInterval(interval) {
    this.trackingInterval = interval;
    
    if (this.isTracking) {
      this.stop();
      this.start();
    }
  }
}

module.exports = MouseTracker;