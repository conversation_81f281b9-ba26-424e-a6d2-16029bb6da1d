const { app, BrowserWindow, screen, ipcMain } = require('electron');
const path = require('path');

class FloatingWindow {
  constructor(options = {}) {
    this.window = null;
    this.isVisible = false;
    this.options = {
      width: options.width || 400,
      height: options.height || 150,
      opacity: options.opacity || 0.9,
      timeout: options.timeout || 5000,
      fontSize: options.fontSize || 14,
      fontFamily: options.fontFamily || 'Microsoft YaHei',
      backgroundColor: options.backgroundColor || '#000000',
      textColor: options.textColor || '#ffffff',
      borderRadius: options.borderRadius || 8,
      padding: options.padding || 15,
      alwaysOnTop: options.alwaysOnTop !== false
    };
    
    this.hideTimer = null;
    this.currentPosition = { x: 0, y: 0 };
    
    this.createWindow();
  }

  createWindow() {
    if (this.window) {
      return;
    }

    this.window = new BrowserWindow({
      width: this.options.width,
      height: this.options.height,
      frame: false,
      transparent: true,
      alwaysOnTop: this.options.alwaysOnTop,
      skipTaskbar: true,
      resizable: false,
      movable: false,
      minimizable: false,
      maximizable: false,
      closable: false,
      focusable: false,
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true
      }
    });

    this.window.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
    this.window.setAlwaysOnTop(true, 'screen-saver');

    const htmlContent = this.generateHTML();
    this.window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

    this.window.on('closed', () => {
      this.window = null;
    });

    this.window.webContents.on('dom-ready', () => {
      console.log('悬浮窗已准备就绪');
    });
  }

  generateHTML() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '${this.options.fontFamily}', sans-serif;
            background: transparent;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: ${this.options.backgroundColor};
            color: ${this.options.textColor};
            padding: ${this.options.padding}px;
            border-radius: ${this.options.borderRadius}px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 100%;
            max-height: 100%;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .content {
            font-size: ${this.options.fontSize}px;
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 6;
            -webkit-box-orient: vertical;
        }
        
        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #888;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #333;
            border-top: 2px solid #888;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .fade-out {
            animation: fadeOut 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <div class="content" id="content">
            <div class="loading">
                <div class="spinner"></div>
                正在翻译...
            </div>
        </div>
    </div>
    
    <script>
        const { ipcRenderer } = require('electron');
        
        ipcRenderer.on('update-content', (event, data) => {
            const contentElement = document.getElementById('content');
            contentElement.innerHTML = data.content;
            
            if (data.isLoading) {
                contentElement.innerHTML = \`
                    <div class="loading">
                        <div class="spinner"></div>
                        \${data.content}
                    </div>
                \`;
            }
        });
        
        ipcRenderer.on('show-window', () => {
            document.querySelector('.container').classList.add('fade-in');
            document.querySelector('.container').classList.remove('fade-out');
        });
        
        ipcRenderer.on('hide-window', () => {
            document.querySelector('.container').classList.add('fade-out');
            document.querySelector('.container').classList.remove('fade-in');
        });
    </script>
</body>
</html>`;
  }

  show(content, position = null) {
    if (!this.window) {
      this.createWindow();
    }

    this.updateContent(content);
    
    if (position) {
      this.updatePosition(position);
    }

    this.window.setOpacity(this.options.opacity);
    this.window.show();
    this.window.webContents.send('show-window');
    
    this.isVisible = true;
    this.resetHideTimer();
    
    console.log('悬浮窗已显示');
  }

  hide() {
    if (!this.window || !this.isVisible) {
      return;
    }

    this.window.webContents.send('hide-window');
    
    setTimeout(() => {
      if (this.window) {
        this.window.hide();
        this.isVisible = false;
      }
    }, 300);

    this.clearHideTimer();
    console.log('悬浮窗已隐藏');
  }

  updateContent(content, isLoading = false) {
    if (!this.window) {
      return;
    }

    const data = {
      content: this.escapeHtml(content),
      isLoading: isLoading
    };

    this.window.webContents.send('update-content', data);
  }

  updatePosition(position) {
    if (!this.window) {
      return;
    }

    const screenBounds = screen.getPrimaryDisplay().bounds;
    let { x, y } = position;

    if (x + this.options.width > screenBounds.width) {
      x = screenBounds.width - this.options.width - 10;
    }

    if (y + this.options.height > screenBounds.height) {
      y = screenBounds.height - this.options.height - 10;
    }

    if (x < 0) x = 10;
    if (y < 0) y = 10;

    this.currentPosition = { x, y };
    this.window.setPosition(x, y);
  }

  showLoading(message = '正在翻译...') {
    this.updateContent(message, true);
  }

  resetHideTimer() {
    this.clearHideTimer();
    
    this.hideTimer = setTimeout(() => {
      this.hide();
    }, this.options.timeout);
  }

  clearHideTimer() {
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  setOptions(newOptions) {
    Object.assign(this.options, newOptions);
    
    if (this.window) {
      this.window.setSize(this.options.width, this.options.height);
      this.window.setOpacity(this.options.opacity);
      
      const htmlContent = this.generateHTML();
      this.window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);
    }
  }

  getPosition() {
    return { ...this.currentPosition };
  }

  isWindowVisible() {
    return this.isVisible;
  }

  destroy() {
    this.clearHideTimer();
    
    if (this.window) {
      this.window.close();
      this.window = null;
    }
    
    this.isVisible = false;
  }
}

module.exports = FloatingWindow;